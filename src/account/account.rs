use crate::account::balance::BalanceManager;
use crate::account::position::{Position, PositionSummary};
use crate::account::types::{AccountConfig, AccountStats, AccountType, RiskMetrics, TradeRecord};
use crate::types::{OrderSide, Price};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 账户主体结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Account {
    /// 账户ID
    pub account_id: String,
    /// 账户配置
    pub config: AccountConfig,
    /// 余额管理器
    pub balance_manager: BalanceManager,
    /// 仓位管理（按交易对）
    pub positions: HashMap<String, Position>,
    /// 交易记录
    pub trade_history: Vec<TradeRecord>,
    /// 账户统计信息
    pub stats: AccountStats,
    /// 风险指标
    pub risk_metrics: RiskMetrics,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl Account {
    /// 创建新账户
    pub fn new(account_id: String, config: AccountConfig) -> Self {
        let mut balance_manager = BalanceManager::new();

        // 初始化USDT余额
        balance_manager.initialize_balance("USDT".to_string(), config.initial_balance);

        let mut account = Self {
            account_id,
            config,
            balance_manager,
            positions: HashMap::new(),
            trade_history: Vec::new(),
            stats: AccountStats::default(),
            risk_metrics: RiskMetrics::default(),
            created_at: Utc::now(),
            last_updated: Utc::now(),
        };

        // 初始化统计信息
        account.update_stats(&HashMap::new());
        account
    }

    /// 获取或创建仓位
    pub fn get_or_create_position(&mut self, symbol: &str) -> &mut Position {
        self.positions
            .entry(symbol.to_string())
            .or_insert_with(|| Position::new(symbol.to_string(), self.config.max_leverage))
    }

    /// 处理交易
    pub fn process_trade(
        &mut self,
        trade: TradeRecord,
        current_prices: &HashMap<String, Price>,
    ) -> Result<(), String> {
        // 验证交易对是否支持
        if !self.config.supported_symbols.contains(&trade.symbol) {
            return Err(format!("Unsupported symbol: {}", trade.symbol));
        }

        // 获取当前价格
        let current_price = current_prices
            .get(&trade.symbol)
            .copied()
            .unwrap_or(trade.price);

        // 处理手续费
        self.balance_manager
            .subtract_balance(&trade.fee_asset, trade.fee)?;

        // 期货交易：不直接扣除余额，而是处理保证金
        let notional_value = trade.notional_value();
        let max_leverage = self.config.max_leverage; // 先获取杠杆值
        let required_margin = notional_value / max_leverage; // 计算所需保证金

        // 检查是否有足够的可用余额作为保证金
        let available_balance = self.balance_manager.get_available_balance("USDT");
        if available_balance < required_margin {
            return Err("Insufficient margin".to_string());
        }

        // 获取当前仓位信息来判断是否需要冻结保证金
        let current_position_qty = self
            .positions
            .get(&trade.symbol)
            .map(|p| p.quantity)
            .unwrap_or(0.0);

        // 冻结保证金（如果是开仓或增加仓位）
        if (trade.side == OrderSide::Buy && current_position_qty >= 0.0)
            || (trade.side == OrderSide::Sell && current_position_qty <= 0.0)
        {
            // 开仓或增加仓位：冻结额外保证金
            self.balance_manager
                .freeze_balance("USDT", required_margin)?;
        }

        // 更新仓位
        let position = self.get_or_create_position(&trade.symbol);
        let realized_pnl = position.process_trade(&trade, current_price)?;

        // 如果有已实现盈亏，更新USDT余额
        if realized_pnl != 0.0 {
            if realized_pnl > 0.0 {
                self.balance_manager.add_balance("USDT", realized_pnl);
            } else {
                self.balance_manager
                    .subtract_balance("USDT", -realized_pnl)?;
            }
        }

        // 记录交易
        self.trade_history.push(trade);

        // 更新统计信息
        self.update_stats(current_prices);
        // 更新风险指标
        self.update_risk_metrics();
        self.last_updated = Utc::now();

        Ok(())
    }

    /// 更新账户统计信息
    pub fn update_stats(&mut self, current_prices: &HashMap<String, Price>) {
        // 计算总资产价值
        let mut total_value = self.balance_manager.get_total_balance("USDT");
        let mut used_margin = 0.0;
        let mut unrealized_pnl = 0.0;
        let mut position_count = 0;

        // 遍历所有仓位
        for position in self.positions.values_mut() {
            if !position.is_empty() {
                if let Some(&current_price) = current_prices.get(&position.symbol) {
                    position.update_unrealized_pnl(current_price);
                    position.update_margin(current_price);

                    unrealized_pnl += position.unrealized_pnl;
                    used_margin += position.margin;
                    position_count += 1;
                }
            }
        }

        total_value += unrealized_pnl;

        // 计算已实现盈亏
        let realized_pnl: f64 = self.positions.values().map(|p| p.realized_pnl).sum();

        // 计算当前杠杆
        let available_balance = self.balance_manager.get_available_balance("USDT");
        let current_leverage = if available_balance > 0.0 {
            used_margin / available_balance
        } else {
            0.0
        };

        // 计算保证金率
        let margin_ratio = if used_margin > 0.0 {
            (available_balance + unrealized_pnl) / used_margin
        } else {
            f64::INFINITY
        };

        // 计算总手续费
        let total_fees: f64 = self.trade_history.iter().map(|t| t.fee).sum();

        self.stats = AccountStats {
            total_value,
            available_balance,
            used_margin,
            unrealized_pnl,
            realized_pnl,
            current_leverage,
            margin_ratio,
            position_count,
            total_trades: self.trade_history.len(),
            total_fees,
            last_updated: Utc::now(),
        };
    }

    /// 更新风险指标
    pub fn update_risk_metrics(&mut self) {
        if self.trade_history.is_empty() {
            return;
        }

        // 计算胜率
        let winning_trades = self
            .trade_history
            .iter()
            .filter(|trade| {
                // 根据交易方向计算盈亏
                let pnl = match trade.side {
                    OrderSide::Buy => {
                        // 买入交易，如果后续价格上涨则盈利
                        // 这里简化处理，假设所有买入都是盈利的（实际应该根据平仓价格计算）
                        1.0
                    }
                    OrderSide::Sell => {
                        // 卖出交易，如果后续价格下跌则盈利
                        // 这里简化处理，假设所有卖出都是盈利的
                        1.0
                    }
                };
                pnl > 0.0
            })
            .count();

        let win_rate = (winning_trades as f64 / self.trade_history.len() as f64) * 100.0;

        // 计算最大回撤
        let mut max_drawdown = 0.0;
        let mut peak_value = self.config.initial_balance;
        let mut current_value = self.config.initial_balance;

        // 按时间顺序计算净值变化
        for trade in &self.trade_history {
            // 简化计算：假设每笔交易的盈亏为手续费的负值
            let trade_pnl = -trade.fee; // 简化处理
            current_value += trade_pnl;

            if current_value > peak_value {
                peak_value = current_value;
            }

            let drawdown = (peak_value - current_value) / peak_value * 100.0;
            if drawdown > max_drawdown {
                max_drawdown = drawdown;
            }
        }

        // 计算盈亏比
        let profits: Vec<f64> = self
            .trade_history
            .iter()
            .map(|t| -t.fee) // 简化处理
            .filter(|&pnl| pnl > 0.0)
            .collect();

        let losses: Vec<f64> = self
            .trade_history
            .iter()
            .map(|t| -t.fee) // 简化处理
            .filter(|&pnl| pnl < 0.0)
            .map(|pnl| -pnl) // 转为正值
            .collect();

        let avg_profit = if !profits.is_empty() {
            profits.iter().sum::<f64>() / profits.len() as f64
        } else {
            0.0
        };

        let avg_loss = if !losses.is_empty() {
            losses.iter().sum::<f64>() / losses.len() as f64
        } else {
            1.0 // 避免除零
        };

        let profit_loss_ratio = if avg_loss > 0.0 {
            avg_profit / avg_loss
        } else {
            0.0
        };

        // 计算最大单笔盈利和亏损
        let max_profit = profits.iter().fold(0.0f64, |a, &b| a.max(b));
        let max_loss = losses.iter().fold(0.0f64, |a, &b| a.max(b));

        self.risk_metrics = RiskMetrics {
            max_drawdown,
            sharpe_ratio: 0.0, // 需要更复杂的计算
            win_rate,
            profit_loss_ratio,
            var_95: 0.0, // 需要更复杂的计算
            max_loss,
            max_profit,
        };
    }

    /// 获取账户摘要
    pub fn get_summary(&self, current_prices: &HashMap<String, Price>) -> AccountSummary {
        let position_summaries: Vec<PositionSummary> = self
            .positions
            .values()
            .filter(|p| !p.is_empty())
            .map(|p| {
                let current_price = current_prices
                    .get(&p.symbol)
                    .copied()
                    .unwrap_or(p.avg_price);
                p.get_summary(current_price)
            })
            .collect();

        // 获取实时的可用余额（考虑冻结保证金）
        let current_available_balance = self.balance_manager.get_available_balance("USDT");

        // 创建更新的统计信息，使用实时的可用余额
        let mut updated_stats = self.stats.clone();
        updated_stats.available_balance = current_available_balance;

        AccountSummary {
            account_id: self.account_id.clone(),
            account_type: self.config.account_type.clone(),
            stats: updated_stats,
            positions: position_summaries,
            balances: self.balance_manager.get_non_zero_balances(),
            risk_metrics: self.risk_metrics.clone(),
            last_updated: self.last_updated,
        }
    }

    /// 检查是否有足够资金进行交易
    pub fn can_afford_trade(
        &self,
        symbol: &str,
        side: OrderSide,
        price: Price,
        quantity: f64,
    ) -> bool {
        match side {
            OrderSide::Buy => {
                let cost = price.value() * quantity;
                let fee = cost * self.config.taker_fee_rate;
                let total_cost = cost + fee;
                self.balance_manager
                    .has_sufficient_balance("USDT", total_cost)
            }
            OrderSide::Sell => {
                // 对于卖出，需要检查是否有足够的仓位
                if let Some(position) = self.positions.get(symbol) {
                    position.quantity >= quantity
                } else {
                    false
                }
            }
        }
    }

    /// 计算最大可交易数量
    pub fn calculate_max_trade_quantity(&self, symbol: &str, side: OrderSide, price: Price) -> f64 {
        match side {
            OrderSide::Buy => {
                let available_balance = self.balance_manager.get_available_balance("USDT");
                let unit_cost = price.value() * (1.0 + self.config.taker_fee_rate);
                if unit_cost > 0.0 {
                    available_balance / unit_cost
                } else {
                    0.0
                }
            }
            OrderSide::Sell => {
                if let Some(position) = self.positions.get(symbol) {
                    position.quantity.max(0.0)
                } else {
                    0.0
                }
            }
        }
    }

    /// 获取仓位信息
    pub fn get_position(&self, symbol: &str) -> Option<&Position> {
        self.positions.get(symbol)
    }

    /// 获取所有非空仓位
    pub fn get_active_positions(&self) -> HashMap<String, &Position> {
        self.positions
            .iter()
            .filter(|(_, position)| !position.is_empty())
            .map(|(symbol, position)| (symbol.clone(), position))
            .collect()
    }

    /// 计算账户净值
    pub fn calculate_net_value(&self, current_prices: &HashMap<String, Price>) -> f64 {
        let mut net_value = self.balance_manager.get_total_balance("USDT");

        for position in self.positions.values() {
            if !position.is_empty() {
                if let Some(&current_price) = current_prices.get(&position.symbol) {
                    net_value += position.calculate_unrealized_pnl(current_price);
                }
            }
        }

        net_value
    }

    /// 验证账户状态
    pub fn validate(&self) -> Result<(), String> {
        // 验证余额
        self.balance_manager.validate_all()?;

        // 验证仓位
        for (symbol, position) in &self.positions {
            if position.symbol != *symbol {
                return Err(format!(
                    "Position symbol mismatch: {} != {}",
                    position.symbol, symbol
                ));
            }
        }

        Ok(())
    }
}

/// 账户摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountSummary {
    pub account_id: String,
    pub account_type: AccountType,
    pub stats: AccountStats,
    pub positions: Vec<PositionSummary>,
    pub balances: HashMap<String, crate::account::balance::Balance>,
    pub risk_metrics: RiskMetrics,
    pub last_updated: DateTime<Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::OrderSide;

    #[test]
    fn test_account_creation() {
        let config = AccountConfig::default();
        let account = Account::new("test_account".to_string(), config.clone());

        assert_eq!(account.account_id, "test_account");
        assert_eq!(account.config.initial_balance, config.initial_balance);
        assert_eq!(
            account.balance_manager.get_available_balance("USDT"),
            config.initial_balance
        );
        assert!(account.positions.is_empty());
        assert!(account.trade_history.is_empty());
    }

    #[test]
    fn test_account_process_buy_trade() {
        let config = AccountConfig::default();
        let mut account = Account::new("test_account".to_string(), config);
        let mut current_prices = HashMap::new();
        current_prices.insert("BTCUSDT".to_string(), Price::new(50000.0));

        let trade_record = TradeRecord::new(
            "trade1".to_string(),
            "order1".to_string(),
            "BTCUSDT".to_string(),
            OrderSide::Buy,
            Price::new(50000.0),
            0.1,
            5.0, // 手续费
            "USDT".to_string(),
            false,
        );

        let result = account.process_trade(trade_record, &current_prices);
        assert!(result.is_ok());

        // 检查余额变化 - 期货交易只扣除手续费和保证金
        let notional_value = 50000.0 * 0.1; // 5000 USDT
        let required_margin = notional_value / 10.0; // 500 USDT (10倍杠杆)
        let fee = 5.0;
        let expected_balance = 10000.0 - fee; // 只扣除手续费
        let expected_available = expected_balance - required_margin; // 可用余额 = 总余额 - 冻结保证金
        assert_eq!(
            account.balance_manager.get_available_balance("USDT"),
            expected_available
        );

        // 检查仓位
        let position = account.get_position("BTCUSDT").unwrap();
        assert_eq!(position.quantity, 0.1);
        assert_eq!(position.avg_price.value(), 50000.0);
    }

    #[test]
    fn test_account_process_sell_trade() {
        let config = AccountConfig::default();
        let mut account = Account::new("test_account".to_string(), config);
        let mut current_prices = HashMap::new();
        current_prices.insert("BTCUSDT".to_string(), Price::new(50000.0));

        // 先买入建立仓位
        let buy_trade = TradeRecord::new(
            "trade1".to_string(),
            "order1".to_string(),
            "BTCUSDT".to_string(),
            OrderSide::Buy,
            Price::new(50000.0),
            0.1,
            5.0,
            "USDT".to_string(),
            false,
        );
        account.process_trade(buy_trade, &current_prices).unwrap();

        // 然后卖出
        let sell_trade = TradeRecord::new(
            "trade2".to_string(),
            "order2".to_string(),
            "BTCUSDT".to_string(),
            OrderSide::Sell,
            Price::new(55000.0),
            0.05,
            2.75, // 手续费
            "USDT".to_string(),
            false,
        );
        account.process_trade(sell_trade, &current_prices).unwrap();

        // 检查仓位变化
        let position = account.get_position("BTCUSDT").unwrap();
        assert_eq!(position.quantity, 0.05); // 剩余0.05
        assert_eq!(position.avg_price.value(), 50000.0); // 平均价格不变
    }

    #[test]
    fn test_account_can_afford_trade() {
        let config = AccountConfig::default();
        let account = Account::new("test_account".to_string(), config);

        // 测试买入
        let can_afford =
            account.can_afford_trade("BTCUSDT", OrderSide::Buy, Price::new(50000.0), 0.1);
        assert!(can_afford); // 应该能够承担

        // 测试买入过大数量
        let cannot_afford =
            account.can_afford_trade("BTCUSDT", OrderSide::Buy, Price::new(50000.0), 1.0);
        assert!(!cannot_afford); // 应该无法承担
    }

    #[test]
    fn test_account_calculate_max_trade_quantity() {
        let config = AccountConfig::default();
        let account = Account::new("test_account".to_string(), config);

        let max_qty =
            account.calculate_max_trade_quantity("BTCUSDT", OrderSide::Buy, Price::new(50000.0));

        // 计算预期最大数量
        let available_balance = 10000.0;
        let unit_cost = 50000.0 * (1.0 + 0.0004); // 价格 * (1 + 手续费率)
        let expected_max = available_balance / unit_cost;

        assert!((max_qty - expected_max).abs() < 1e-8);
    }

    #[test]
    fn test_account_net_value_calculation() {
        let config = AccountConfig::default();
        let mut account = Account::new("test_account".to_string(), config);
        let mut current_prices = HashMap::new();
        current_prices.insert("BTCUSDT".to_string(), Price::new(55000.0)); // 价格上涨

        // 建立仓位
        let trade_record = TradeRecord::new(
            "trade1".to_string(),
            "order1".to_string(),
            "BTCUSDT".to_string(),
            OrderSide::Buy,
            Price::new(50000.0),
            0.1,
            5.0,
            "USDT".to_string(),
            false,
        );
        account
            .process_trade(trade_record, &current_prices)
            .unwrap();

        let net_value = account.calculate_net_value(&current_prices);

        // 计算预期净值 - 期货交易只扣除手续费
        let remaining_usdt = 10000.0 - 5.0; // 只扣除手续费
        let unrealized_pnl = 0.1 * (55000.0 - 50000.0); // 未实现盈亏
        let expected_net_value = remaining_usdt + unrealized_pnl;

        assert!((net_value - expected_net_value).abs() < 1e-8);
    }

    #[test]
    fn test_account_validation() {
        let config = AccountConfig::default();
        let account = Account::new("test_account".to_string(), config);

        let result = account.validate();
        assert!(result.is_ok());
    }

    #[test]
    fn test_account_get_summary() {
        let config = AccountConfig::default();
        let account = Account::new("test_account".to_string(), config.clone());
        let current_prices = HashMap::new();

        let summary = account.get_summary(&current_prices);

        assert_eq!(summary.account_id, "test_account");
        assert_eq!(summary.account_type, config.account_type);
        assert_eq!(summary.stats.available_balance, config.initial_balance);
        assert!(summary.positions.is_empty());
    }
}
