use backtest::backtest_summary::HtmlGenerator;
use backtest::state::{get_backtest_recorder, set_backtest_recorder};
use backtest::types::{
    BacktestBbo, BacktestOrder, BacktestRecorder, BacktestSummary, BacktestTrade, OrderSide,
    OrderStatus, OrderType, Price,
};
use chrono::Utc;
use std::sync::Arc;
use tokio::sync::Mutex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 回测总结功能示例");
    println!("==================");

    // 1. 初始化回测记录器
    println!("1. 初始化回测记录器...");
    let recorder = Arc::new(Mutex::new(BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;
    println!("   ✓ 回测记录器已初始化");

    // 2. 开始记录
    println!("2. 开始回测记录...");
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }
    println!("   ✓ 回测记录已开始");

    // 3. 模拟回测过程 - 记录BBO数据
    println!("3. 记录市场数据...");

    // 使用过去的时间戳作为基准时间
    let base_time = Utc::now() - chrono::Duration::minutes(10); // 10分钟前开始

    {
        let mut recorder = recorder.lock().await;

        // 模拟一些BBO数据
        for i in 0..10 {
            let bbo = backtest::types::Bbo {
                update_id: i,
                bid_price: Price::new(50000.0 + i as f64),
                bid_quantity: 1.0,
                ask_price: Price::new(50001.0 + i as f64),
                ask_quantity: 1.0,
                timestamp: Some(
                    (base_time + chrono::Duration::seconds(i as i64)).timestamp_micros() as u64,
                ),
                data_source_type: backtest::config::DataSourceType::BinanceOfficial,
            };
            recorder.record_bbo(&bbo);
        }
        println!("   ✓ 记录了 {} 条BBO数据", recorder.bbo_records.len());
    }

    // 4. 模拟回测过程 - 记录订单
    println!("4. 记录订单数据...");
    {
        let mut recorder = recorder.lock().await;

        // 模拟一些订单
        for i in 0..5 {
            let execution_price = 50000.0 + i as f64 - 0.5; // 稍微不同的成交价格
            let order = backtest::types::Order {
                id: format!("order_{}", i),
                client_order_id: format!("client_{}", i),
                symbol: "BTCUSDT".to_string(),
                order_type: if i % 2 == 0 {
                    OrderType::Limit
                } else {
                    OrderType::Market
                },
                side: if i % 2 == 0 {
                    OrderSide::Buy
                } else {
                    OrderSide::Sell
                },
                price: Some(Price::new(50000.0 + i as f64)),
                quantity: 0.1,
                status: OrderStatus::Filled,
                timestamp: base_time + chrono::Duration::seconds(i as i64),
                execution_info: Some(backtest::types::OrderExecutionInfo {
                    last_filled_price: Some(Price::new(execution_price)),
                    last_filled_quantity: 0.1,
                    filled_quantity: 0.1,
                    average_price: Some(Price::new(execution_price)),
                    commission: execution_price * 0.1 * 0.001, // 0.1% 手续费
                    commission_asset: "USDT".to_string(),
                    trade_id: Some(format!("trade_{}", i)),
                }),
            };
            recorder.record_order(&order);
        }
        println!("   ✓ 记录了 {} 条订单", recorder.orders.len());
    }

    // 5. 模拟回测过程 - 记录交易
    println!("5. 记录交易数据...");
    {
        let mut recorder = recorder.lock().await;

        // 模拟一些交易
        for i in 0..3 {
            let trade_record = backtest::account::types::TradeRecord::new(
                format!("trade_{}", i),
                format!("order_{}", i),
                "BTCUSDT".to_string(),
                if i % 2 == 0 {
                    OrderSide::Buy
                } else {
                    OrderSide::Sell
                },
                Price::new(50000.0 + i as f64),
                0.1,
                0.1, // 手续费
                "USDT".to_string(),
                i % 2 == 0, // 交替maker/taker
            );
            recorder.record_trade(&trade_record);
        }
        println!("   ✓ 记录了 {} 条交易", recorder.trades.len());
    }

    // 6. 停止记录
    println!("6. 停止回测记录...");
    {
        let mut recorder = recorder.lock().await;
        recorder.stop_recording(Utc::now());
    }
    println!("   ✓ 回测记录已停止");

    // 7. 生成回测总结
    println!("7. 生成回测总结...");
    let summary = {
        let recorder = recorder.lock().await;
        recorder.generate_summary()
    };

    match summary {
        Some(summary) => {
            println!("   ✓ 回测总结生成成功");
            println!("   📊 统计信息:");
            println!(
                "      - 回测期间: {} - {}",
                summary.start_time.format("%Y-%m-%d %H:%M:%S"),
                summary.end_time.format("%Y-%m-%d %H:%M:%S")
            );
            println!("      - 总盈亏: {:.2} USDT", summary.total_pnl);
            println!("      - 下单次数: {}", summary.total_orders);
            println!("      - 成交次数: {}", summary.total_fills);
            println!("      - 胜率: {:.1}%", summary.win_rate);
            println!("      - 年化收益率: {:.2}%", summary.annual_return);
            println!("      - 最大回撤: {:.2}%", summary.max_drawdown);
            println!("      - 持仓数量: {}", summary.position_count);

            // 8. 生成HTML报告
            println!("8. 生成HTML报告...");
            let html = HtmlGenerator::generate_summary_html(&summary);

            // 保存HTML文件
            let filename = format!(
                "backtest_summary_{}.html",
                Utc::now().format("%Y%m%d_%H%M%S")
            );
            std::fs::write(&filename, html)?;
            println!("   ✓ HTML报告已保存到: {}", filename);
            println!("   🌐 请在浏览器中打开该文件查看完整的回测报告");
        }
        None => {
            println!("   ❌ 生成回测总结失败");
        }
    }

    // 9. 演示HTTP API调用
    println!("9. HTTP API端点:");
    println!("   - POST /api/v1/backtest/start  - 开始回测记录");
    println!("   - POST /api/v1/backtest/stop   - 停止回测记录");
    println!("   - GET  /api/v1/backtest/summary - 获取回测总结报告");

    println!("\n🎉 回测总结功能示例完成！");
    Ok(())
}

/// 演示如何通过HTTP API使用回测总结功能
#[tokio::test]
async fn test_http_api_integration() {
    use warp::test;

    // 注意：这个测试需要在HTTP服务器运行的情况下进行
    // 这里只是演示API调用的方式

    println!("测试HTTP API集成...");

    // 模拟开始回测
    println!("1. 调用开始回测API...");
    // let resp = test::request()
    //     .method("POST")
    //     .path("/api/v1/backtest/start")
    //     .reply(&routes)
    //     .await;
    // assert_eq!(resp.status(), 200);

    // 模拟停止回测
    println!("2. 调用停止回测API...");
    // let resp = test::request()
    //     .method("POST")
    //     .path("/api/v1/backtest/stop")
    //     .reply(&routes)
    //     .await;
    // assert_eq!(resp.status(), 200);

    // 模拟获取总结
    println!("3. 调用获取总结API...");
    // let resp = test::request()
    //     .method("GET")
    //     .path("/api/v1/backtest/summary")
    //     .reply(&routes)
    //     .await;
    // assert_eq!(resp.status(), 200);

    println!("✓ HTTP API集成测试完成");
}
